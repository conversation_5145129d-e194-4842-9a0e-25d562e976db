"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/releases/page",{

/***/ "(app-pages-browser)/./src/components/releases/download-options-dialog.tsx":
/*!*************************************************************!*\
  !*** ./src/components/releases/download-options-dialog.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadOptionsDialog: function() { return /* binding */ DownloadOptionsDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=LoaderCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ DownloadOptionsDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst formatLabels = {\n    \"MP3-320\": \"MP3 320\",\n    \"MP3-V0\": \"MP3 (V0)\",\n    FLAC: \"FLAC\",\n    AAC: \"AAC\",\n    \"Ogg Vorbis\": \"Ogg Vorbis\",\n    ALAC: \"ALAC\",\n    WAV: \"WAV\",\n    AIFF: \"AIFF\"\n};\nconst formatOrder = [\n    \"MP3-320\",\n    \"MP3-V0\",\n    \"AAC\",\n    \"Ogg Vorbis\",\n    \"FLAC\",\n    \"WAV\",\n    \"AIFF\",\n    \"ALAC\"\n];\nfunction DownloadOptionsDialog(param) {\n    let { release, children } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const handleDownload = async (format)=>{\n        setIsLoading(format);\n        try {\n            const folderName = \"\".concat(release.artist, \" - \").concat(release.title);\n            const fileName = \"\".concat(folderName, \" - \").concat(format, \".zip\");\n            // Direct S3 URL - no Lambda needed\n            const s3BaseUrl = \"https://auspex-records-releases.s3.us-west-1.amazonaws.com\";\n            // Use + encoding for spaces to match S3 bucket structure\n            const encodedFolderName = folderName.replace(/ /g, \"+\");\n            const encodedFileName = fileName.replace(/ /g, \"+\");\n            const downloadUrl = \"\".concat(s3BaseUrl, \"/\").concat(encodedFolderName, \"/\").concat(encodedFileName);\n            // Skip HEAD request due to CORS issues - directly open download link\n            // The S3 bucket will return 404 if file doesn't exist\n            window.open(downloadUrl, \"_blank\");\n            toast({\n                title: \"Download Started\",\n                description: \"Downloading \".concat(fileName)\n            });\n        } catch (error) {\n            console.error(\"Download error:\", error);\n            toast({\n                title: \"Download Failed\",\n                description: \"Could not find the download file. Please try again later.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTrigger, {\n                asChild: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n                className: \"sm:max-w-lg bg-card/80 backdrop-blur-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                className: \"text-primary\",\n                                children: [\n                                    \"Download: \",\n                                    release.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                                className: \"text-foreground/80\",\n                                children: \"Choose your preferred audio format. All downloads are high-quality.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: formatOrder.map((format)=>{\n                                var _formatLabels_format;\n                                const formatLabel = (_formatLabels_format = formatLabels[format]) !== null && _formatLabels_format !== void 0 ? _formatLabels_format : format;\n                                const isButtonLoading = isLoading === format;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>handleDownload(format),\n                                    variant: \"outline\",\n                                    className: \"bg-primary/5 border-primary/20 text-primary hover:bg-primary/10\",\n                                    disabled: isButtonLoading,\n                                    children: isButtonLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 21\n                                    }, this) : formatLabel\n                                }, format, false, {\n                                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(DownloadOptionsDialog, \"ES41gIrS8CiyObkZptFPWRuCm+o=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = DownloadOptionsDialog;\nvar _c;\n$RefreshReg$(_c, \"DownloadOptionsDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3JlbGVhc2VzL2Rvd25sb2FkLW9wdGlvbnMtZGlhbG9nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBR2lDO0FBUUQ7QUFDTTtBQUVPO0FBQ0Q7QUFPNUMsTUFBTVUsZUFBdUM7SUFDM0MsV0FBVztJQUNYLFVBQVU7SUFDVkMsTUFBTTtJQUNOQyxLQUFLO0lBQ0wsY0FBYztJQUNkQyxNQUFNO0lBQ05DLEtBQUs7SUFDTEMsTUFBTTtBQUNSO0FBRUEsTUFBTUMsY0FBd0I7SUFDNUI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNEO0FBR00sU0FBU0Msc0JBQXNCLEtBQWlEO1FBQWpELEVBQUVDLE9BQU8sRUFBRUMsUUFBUSxFQUE4QixHQUFqRDs7SUFDcEMsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdyQiwrQ0FBUUEsQ0FBZ0I7SUFDMUQsTUFBTSxFQUFFc0IsS0FBSyxFQUFFLEdBQUdkLDBEQUFRQTtJQUUxQixNQUFNZSxpQkFBaUIsT0FBT0M7UUFDNUJILGFBQWFHO1FBQ2IsSUFBSTtZQUNGLE1BQU1DLGFBQWEsR0FBdUJQLE9BQXBCQSxRQUFRUSxNQUFNLEVBQUMsT0FBbUIsT0FBZFIsUUFBUVMsS0FBSztZQUN2RCxNQUFNQyxXQUFXLEdBQW1CSixPQUFoQkMsWUFBVyxPQUFZLE9BQVBELFFBQU87WUFFM0MsbUNBQW1DO1lBQ25DLE1BQU1LLFlBQVk7WUFFbEIseURBQXlEO1lBQ3pELE1BQU1DLG9CQUFvQkwsV0FBV00sT0FBTyxDQUFDLE1BQU07WUFDbkQsTUFBTUMsa0JBQWtCSixTQUFTRyxPQUFPLENBQUMsTUFBTTtZQUMvQyxNQUFNRSxjQUFjLEdBQWdCSCxPQUFiRCxXQUFVLEtBQXdCRyxPQUFyQkYsbUJBQWtCLEtBQW1CLE9BQWhCRTtZQUV6RCxxRUFBcUU7WUFDckUsc0RBQXNEO1lBQ3RERSxPQUFPQyxJQUFJLENBQUNGLGFBQWE7WUFFekJYLE1BQU07Z0JBQ0pLLE9BQU87Z0JBQ1BTLGFBQWEsZUFBd0IsT0FBVFI7WUFDOUI7UUFDRixFQUFFLE9BQU9TLE9BQU87WUFDYkMsUUFBUUQsS0FBSyxDQUFDLG1CQUFtQkE7WUFDakNmLE1BQU07Z0JBQ0xLLE9BQU87Z0JBQ1BTLGFBQWE7Z0JBQ2JHLFNBQVM7WUFDVjtRQUNILFNBQVU7WUFDUmxCLGFBQWE7UUFDZjtJQUNGO0lBRUEscUJBQ0UsOERBQUNwQix5REFBTUE7OzBCQUNMLDhEQUFDSyxnRUFBYUE7Z0JBQUNrQyxPQUFPOzBCQUFFckI7Ozs7OzswQkFDeEIsOERBQUNqQixnRUFBYUE7Z0JBQUN1QyxXQUFVOztrQ0FDdkIsOERBQUNyQywrREFBWUE7OzBDQUNYLDhEQUFDQyw4REFBV0E7Z0NBQUNvQyxXQUFVOztvQ0FBZTtvQ0FBV3ZCLFFBQVFTLEtBQUs7Ozs7Ozs7MENBQzlELDhEQUFDeEIsb0VBQWlCQTtnQ0FBQ3NDLFdBQVU7MENBQXFCOzs7Ozs7Ozs7Ozs7a0NBSXBELDhEQUFDQzt3QkFBSUQsV0FBVTtrQ0FDYiw0RUFBQ0M7NEJBQUlELFdBQVU7c0NBQ1p6QixZQUFZMkIsR0FBRyxDQUFDLENBQUNuQjtvQ0FDSWQ7Z0NBQXBCLE1BQU1rQyxjQUFjbEMsQ0FBQUEsdUJBQUFBLFlBQVksQ0FBQ2MsT0FBb0MsY0FBakRkLGtDQUFBQSx1QkFBcURjO2dDQUN6RSxNQUFNcUIsa0JBQWtCekIsY0FBY0k7Z0NBQ3RDLHFCQUNFLDhEQUFDakIsOENBQU1BO29DQUVMdUMsU0FBUyxJQUFNdkIsZUFBZUM7b0NBQzlCZSxTQUFRO29DQUNSRSxXQUFVO29DQUNWTSxVQUFVRjs4Q0FFVEEsZ0NBQ0MsOERBQUNwQyx3RkFBWUE7d0NBQUNnQyxXQUFVOzs7OzsrQ0FFeEJHO21DQVRHcEI7Ozs7OzRCQWFYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1aO0dBMUVnQlA7O1FBRUlULHNEQUFRQTs7O0tBRlpTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3JlbGVhc2VzL2Rvd25sb2FkLW9wdGlvbnMtZGlhbG9nLnRzeD9kZmIxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQge1xuICBEaWFsb2csXG4gIERpYWxvZ0NvbnRlbnQsXG4gIERpYWxvZ0Rlc2NyaXB0aW9uLFxuICBEaWFsb2dIZWFkZXIsXG4gIERpYWxvZ1RpdGxlLFxuICBEaWFsb2dUcmlnZ2VyLFxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2RpYWxvZ1wiO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIi4uL3VpL2J1dHRvblwiO1xuaW1wb3J0IHR5cGUgeyBSZWxlYXNlIH0gZnJvbSBcIkAvbGliL3R5cGVzXCI7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiO1xuaW1wb3J0IHsgTG9hZGVyQ2lyY2xlIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuXG5pbnRlcmZhY2UgRG93bmxvYWRPcHRpb25zRGlhbG9nUHJvcHMge1xuICByZWxlYXNlOiBSZWxlYXNlO1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5jb25zdCBmb3JtYXRMYWJlbHM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gIFwiTVAzLTMyMFwiOiBcIk1QMyAzMjBcIixcbiAgXCJNUDMtVjBcIjogXCJNUDMgKFYwKVwiLFxuICBGTEFDOiBcIkZMQUNcIixcbiAgQUFDOiBcIkFBQ1wiLFxuICBcIk9nZyBWb3JiaXNcIjogXCJPZ2cgVm9yYmlzXCIsXG4gIEFMQUM6IFwiQUxBQ1wiLFxuICBXQVY6IFwiV0FWXCIsXG4gIEFJRkY6IFwiQUlGRlwiLFxufTtcblxuY29uc3QgZm9ybWF0T3JkZXI6IHN0cmluZ1tdID0gW1xuICBcIk1QMy0zMjBcIixcbiAgXCJNUDMtVjBcIixcbiAgXCJBQUNcIixcbiAgXCJPZ2cgVm9yYmlzXCIsXG4gIFwiRkxBQ1wiLFxuICBcIldBVlwiLFxuICBcIkFJRkZcIixcbiAgXCJBTEFDXCIsXG5dO1xuXG5cbmV4cG9ydCBmdW5jdGlvbiBEb3dubG9hZE9wdGlvbnNEaWFsb2coeyByZWxlYXNlLCBjaGlsZHJlbiB9OiBEb3dubG9hZE9wdGlvbnNEaWFsb2dQcm9wcykge1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KCk7XG5cbiAgY29uc3QgaGFuZGxlRG93bmxvYWQgPSBhc3luYyAoZm9ybWF0OiBzdHJpbmcpID0+IHtcbiAgICBzZXRJc0xvYWRpbmcoZm9ybWF0KTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZm9sZGVyTmFtZSA9IGAke3JlbGVhc2UuYXJ0aXN0fSAtICR7cmVsZWFzZS50aXRsZX1gO1xuICAgICAgY29uc3QgZmlsZU5hbWUgPSBgJHtmb2xkZXJOYW1lfSAtICR7Zm9ybWF0fS56aXBgO1xuXG4gICAgICAvLyBEaXJlY3QgUzMgVVJMIC0gbm8gTGFtYmRhIG5lZWRlZFxuICAgICAgY29uc3QgczNCYXNlVXJsID0gJ2h0dHBzOi8vYXVzcGV4LXJlY29yZHMtcmVsZWFzZXMuczMudXMtd2VzdC0xLmFtYXpvbmF3cy5jb20nO1xuXG4gICAgICAvLyBVc2UgKyBlbmNvZGluZyBmb3Igc3BhY2VzIHRvIG1hdGNoIFMzIGJ1Y2tldCBzdHJ1Y3R1cmVcbiAgICAgIGNvbnN0IGVuY29kZWRGb2xkZXJOYW1lID0gZm9sZGVyTmFtZS5yZXBsYWNlKC8gL2csICcrJyk7XG4gICAgICBjb25zdCBlbmNvZGVkRmlsZU5hbWUgPSBmaWxlTmFtZS5yZXBsYWNlKC8gL2csICcrJyk7XG4gICAgICBjb25zdCBkb3dubG9hZFVybCA9IGAke3MzQmFzZVVybH0vJHtlbmNvZGVkRm9sZGVyTmFtZX0vJHtlbmNvZGVkRmlsZU5hbWV9YDtcblxuICAgICAgLy8gU2tpcCBIRUFEIHJlcXVlc3QgZHVlIHRvIENPUlMgaXNzdWVzIC0gZGlyZWN0bHkgb3BlbiBkb3dubG9hZCBsaW5rXG4gICAgICAvLyBUaGUgUzMgYnVja2V0IHdpbGwgcmV0dXJuIDQwNCBpZiBmaWxlIGRvZXNuJ3QgZXhpc3RcbiAgICAgIHdpbmRvdy5vcGVuKGRvd25sb2FkVXJsLCAnX2JsYW5rJyk7XG5cbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiRG93bmxvYWQgU3RhcnRlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogYERvd25sb2FkaW5nICR7ZmlsZU5hbWV9YCxcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgY29uc29sZS5lcnJvcihcIkRvd25sb2FkIGVycm9yOlwiLCBlcnJvcik7XG4gICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJEb3dubG9hZCBGYWlsZWRcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQ291bGQgbm90IGZpbmQgdGhlIGRvd25sb2FkIGZpbGUuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKG51bGwpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxEaWFsb2c+XG4gICAgICA8RGlhbG9nVHJpZ2dlciBhc0NoaWxkPntjaGlsZHJlbn08L0RpYWxvZ1RyaWdnZXI+XG4gICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJzbTptYXgtdy1sZyBiZy1jYXJkLzgwIGJhY2tkcm9wLWJsdXItbGdcIj5cbiAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICA8RGlhbG9nVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5XCI+RG93bmxvYWQ6IHtyZWxlYXNlLnRpdGxlfTwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtZm9yZWdyb3VuZC84MFwiPlxuICAgICAgICAgICAgQ2hvb3NlIHlvdXIgcHJlZmVycmVkIGF1ZGlvIGZvcm1hdC4gQWxsIGRvd25sb2FkcyBhcmUgaGlnaC1xdWFsaXR5LlxuICAgICAgICAgIDwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTQgcHktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAge2Zvcm1hdE9yZGVyLm1hcCgoZm9ybWF0KSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGZvcm1hdExhYmVsID0gZm9ybWF0TGFiZWxzW2Zvcm1hdCBhcyBrZXlvZiB0eXBlb2YgZm9ybWF0TGFiZWxzXSA/PyBmb3JtYXQ7XG4gICAgICAgICAgICAgIGNvbnN0IGlzQnV0dG9uTG9hZGluZyA9IGlzTG9hZGluZyA9PT0gZm9ybWF0O1xuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgICBrZXk9e2Zvcm1hdH0gXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEb3dubG9hZChmb3JtYXQpfVxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHJpbWFyeS81IGJvcmRlci1wcmltYXJ5LzIwIHRleHQtcHJpbWFyeSBob3ZlcjpiZy1wcmltYXJ5LzEwXCJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0J1dHRvbkxvYWRpbmd9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzQnV0dG9uTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgPExvYWRlckNpcmNsZSBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgZm9ybWF0TGFiZWxcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICA8L0RpYWxvZz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dEZXNjcmlwdGlvbiIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nVHJpZ2dlciIsIkJ1dHRvbiIsInVzZVRvYXN0IiwiTG9hZGVyQ2lyY2xlIiwiZm9ybWF0TGFiZWxzIiwiRkxBQyIsIkFBQyIsIkFMQUMiLCJXQVYiLCJBSUZGIiwiZm9ybWF0T3JkZXIiLCJEb3dubG9hZE9wdGlvbnNEaWFsb2ciLCJyZWxlYXNlIiwiY2hpbGRyZW4iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ0b2FzdCIsImhhbmRsZURvd25sb2FkIiwiZm9ybWF0IiwiZm9sZGVyTmFtZSIsImFydGlzdCIsInRpdGxlIiwiZmlsZU5hbWUiLCJzM0Jhc2VVcmwiLCJlbmNvZGVkRm9sZGVyTmFtZSIsInJlcGxhY2UiLCJlbmNvZGVkRmlsZU5hbWUiLCJkb3dubG9hZFVybCIsIndpbmRvdyIsIm9wZW4iLCJkZXNjcmlwdGlvbiIsImVycm9yIiwiY29uc29sZSIsInZhcmlhbnQiLCJhc0NoaWxkIiwiY2xhc3NOYW1lIiwiZGl2IiwibWFwIiwiZm9ybWF0TGFiZWwiLCJpc0J1dHRvbkxvYWRpbmciLCJvbkNsaWNrIiwiZGlzYWJsZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/releases/download-options-dialog.tsx\n"));

/***/ })

});