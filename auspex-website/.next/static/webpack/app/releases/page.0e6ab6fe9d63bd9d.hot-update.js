"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/releases/page",{

/***/ "(app-pages-browser)/./src/components/releases/download-options-dialog.tsx":
/*!*************************************************************!*\
  !*** ./src/components/releases/download-options-dialog.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DownloadOptionsDialog: function() { return /* binding */ DownloadOptionsDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=LoaderCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ DownloadOptionsDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst formatLabels = {\n    \"MP3-320\": \"MP3 320\",\n    \"MP3-V0\": \"MP3 (V0)\",\n    FLAC: \"FLAC\",\n    AAC: \"AAC\",\n    \"Ogg Vorbis\": \"Ogg Vorbis\",\n    ALAC: \"ALAC\",\n    WAV: \"WAV\",\n    AIFF: \"AIFF\"\n};\nconst formatOrder = [\n    \"MP3-320\",\n    \"MP3-V0\",\n    \"AAC\",\n    \"Ogg Vorbis\",\n    \"FLAC\",\n    \"WAV\",\n    \"AIFF\",\n    \"ALAC\"\n];\nfunction DownloadOptionsDialog(param) {\n    let { release, children } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const handleDownload = async (format)=>{\n        setIsLoading(format);\n        try {\n            const folderName = \"\".concat(release.artist, \" - \").concat(release.title);\n            const fileName = \"\".concat(folderName, \" - \").concat(format, \".zip\");\n            // Direct S3 URL - no Lambda needed\n            const s3BaseUrl = \"https://auspex-records-releases.s3.us-west-1.amazonaws.com\";\n            // Use + encoding for spaces to match S3 bucket structure\n            const encodedFolderName = folderName.replace(/ /g, \"+\");\n            const encodedFileName = fileName.replace(/ /g, \"+\");\n            const downloadUrl = \"\".concat(s3BaseUrl, \"/\").concat(encodedFolderName, \"/\").concat(encodedFileName);\n            // Test if file exists before opening\n            const response = await fetch(downloadUrl, {\n                method: \"HEAD\"\n            });\n            if (!response.ok) {\n                throw new Error(\"File not found: \".concat(fileName));\n            }\n            // Open download link\n            window.open(downloadUrl, \"_blank\");\n            toast({\n                title: \"Download Started\",\n                description: \"Downloading \".concat(fileName)\n            });\n        } catch (error) {\n            console.error(\"Download error:\", error);\n            toast({\n                title: \"Download Failed\",\n                description: \"Could not find the download file. Please try again later.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTrigger, {\n                asChild: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n                className: \"sm:max-w-lg bg-card/80 backdrop-blur-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                className: \"text-primary\",\n                                children: [\n                                    \"Download: \",\n                                    release.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                                className: \"text-foreground/80\",\n                                children: \"Choose your preferred audio format. All downloads are high-quality.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: formatOrder.map((format)=>{\n                                var _formatLabels_format;\n                                const formatLabel = (_formatLabels_format = formatLabels[format]) !== null && _formatLabels_format !== void 0 ? _formatLabels_format : format;\n                                const isButtonLoading = isLoading === format;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>handleDownload(format),\n                                    variant: \"outline\",\n                                    className: \"bg-primary/5 border-primary/20 text-primary hover:bg-primary/10\",\n                                    disabled: isButtonLoading,\n                                    children: isButtonLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 21\n                                    }, this) : formatLabel\n                                }, format, false, {\n                                    fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Workspace/auspex/auspex-website/src/components/releases/download-options-dialog.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(DownloadOptionsDialog, \"ES41gIrS8CiyObkZptFPWRuCm+o=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = DownloadOptionsDialog;\nvar _c;\n$RefreshReg$(_c, \"DownloadOptionsDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/releases/download-options-dialog.tsx\n"));

/***/ })

});