"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_2FUsers_2Fvishal_2FWorkspace_2Fauspex_2Fauspex_website_2Fsrc_2Fapp_2Ffavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"export\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_2FUsers_2Fvishal_2FWorkspace_2Fauspex_2Fauspex_website_2Fsrc_2Fapp_2Ffavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/favicon.ico/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__ ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"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\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp%2Ffavicon.ico&isDynamic=0!?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fvishal%2FWorkspace%2Fauspex%2Fauspex-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();